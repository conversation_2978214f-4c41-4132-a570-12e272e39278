from langchain_openai import ChatOpenAI
from dotenv import load_dotenv
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import StrOutputParser

load_dotenv()

prompt1 = PromptTemplate(
    template='Generate a detailed report on {topic}',
    input_variables=['topic']
)

prompt2 = PromptTemplate(
    template='Generate a 5 pointer summary from the following text \n {text}',
    input_variables=['text']
)

model = ChatOpenAI(model='gpt-4o',temperature=0.7,api_key)
mode2 = ChatOpenAI(model='gpt-4o-mini',temperature=0.5,api_key)

parser = StrOutputParser()

chain = prompt1 | model | parser | prompt2 | mode2 | parser

config = {
    "run_name": "Sequential Chain",
    "tags": ["LLM App", "Report Generation", "Summarization"],
    "metadata": {"Model1": "gpt-4o","Model1_Temperature": 0.7, "Model2": "gpt-4o-mini", "Model2_Temperature": 0.5, "Parser": "StrOutputParser"}
}

result = chain.invoke({'topic': 'Unemployment in Pakistan'},config=config)

print(result)
