# LangSmith Learning Project

This repository contains examples and experiments with **Lang<PERSON>mith**, a powerful platform for building, monitoring, and optimizing LLM applications using LangChain.

## What is LangSmith?

LangSmith is a comprehensive platform developed by LangChain that provides:

- **Observability**: Monitor and trace your LLM applications in real-time
- **Evaluation**: Test and evaluate your LLM chains with custom datasets
- **Debugging**: Identify bottlenecks and issues in your LLM workflows
- **Optimization**: Improve performance and reduce costs of your AI applications
- **Collaboration**: Share and collaborate on LLM projects with your team

LangSmith helps developers build production-ready LLM applications by providing insights into how your chains are performing, where they might be failing, and how to improve them.

## Project Structure

This project demonstrates various LangChain patterns and integrations with LangSmith:

### Files Overview

- **`1_simple_llm_call.py`** - Basic LLM chain example
- **`2_sequential_chain.py`** - Advanced sequential chain with monitoring
- **`requirements.txt`** - Project dependencies
- **`langsmith/`** - Virtual environment directory

## Code Examples

### 1. Simple LLM Call (`1_simple_llm_call.py`)

A basic example demonstrating:
- Simple prompt template creation
- OpenAI model integration
- Basic chain composition (prompt → model → parser)
- Environment variable management with dotenv

```python
# Chain: prompt → model → parser
chain = prompt | model | parser
result = chain.invoke({"question": "What is the capital of Azerbaijan?"})
```

### 2. Sequential Chain (`2_sequential_chain.py`)

An advanced example showcasing:
- **Multi-step processing**: Report generation followed by summarization
- **Multiple models**: Uses both GPT-4o and GPT-4o-mini for different tasks
- **LangSmith integration**: Includes run configuration with tags and metadata
- **Chain composition**: Complex pipeline with multiple prompts and models

Key features:
- Generates detailed reports on specified topics
- Summarizes reports into 5-point summaries
- Uses different temperature settings for different tasks
- Includes comprehensive LangSmith tracking configuration

```python
# Complex chain with monitoring
config = {
    "run_name": "Sequential Chain",
    "tags": ["LLM App", "Report Generation", "Summarization"],
    "metadata": {"Model1": "gpt-4o", "Model2": "gpt-4o-mini", ...}
}
```

## Dependencies

This project uses several key libraries:

- **LangChain** (`langchain==0.3.27`) - Core framework for LLM applications
- **LangSmith** (`langsmith==0.4.13`) - Monitoring and evaluation platform
- **OpenAI** (`langchain-openai==0.3.29`) - OpenAI model integration
- **Python-dotenv** - Environment variable management

## Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd LangSmith
   ```

2. **Create and activate virtual environment**
   ```bash
   python -m venv langsmith
   source langsmith/bin/activate  # On Windows: langsmith\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   Create a `.env` file with your API keys:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   LANGCHAIN_API_KEY=your_langsmith_api_key_here
   LANGCHAIN_TRACING_V2=true
   ```

5. **Run the examples**
   ```bash
   python 1_simple_llm_call.py
   python 2_sequential_chain.py
   ```

## Learning Objectives

This project helps you understand:

- Basic LangChain chain composition
- Integration with OpenAI models
- Sequential processing with multiple LLMs
- LangSmith monitoring and tracing
- Best practices for LLM application development

## Next Steps

- Explore more complex chain patterns
- Implement custom evaluation metrics
- Add error handling and retry logic
- Experiment with different model combinations
- Build interactive applications with Streamlit

## Resources

- [LangChain Documentation](https://python.langchain.com/)
- [LangSmith Documentation](https://docs.smith.langchain.com/)
- [OpenAI API Documentation](https://platform.openai.com/docs/)

---

*This project is part of learning LangChain and LangSmith for building production-ready LLM applications.*