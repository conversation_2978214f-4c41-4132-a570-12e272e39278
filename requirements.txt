aiohappyeyeballs==2.6.1
aiohttp==3.12.15
aiosignal==1.4.0
aiosqlite==0.21.0
altair==5.5.0
annotated-types==0.7.0
anyio==4.10.0
attrs==25.3.0
blinker==1.9.0
cachetools==6.1.0
certifi==2025.8.3
charset-normalizer==3.4.3
click==8.2.1
dataclasses-json==0.6.7
defusedxml==0.7.1
distro==1.9.0
dotenv==0.9.9
duckduckgo_search==8.1.1
faiss-cpu==1.11.0.post1
frozenlist==1.7.0
gitdb==4.0.12
GitPython==3.1.45
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
httpx-sse==0.4.1
idna==3.10
Jinja2==3.1.6
jiter==0.10.0
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
langchain==0.3.27
langchain-community==0.3.27
langchain-core==0.3.74
langchain-openai==0.3.29
langchain-text-splitters==0.3.9
langgraph==0.6.4
langgraph-checkpoint==2.1.1
langgraph-checkpoint-sqlite==2.0.11
langgraph-prebuilt==0.6.4
langgraph-sdk==0.2.0
langsmith==0.4.13
lxml==6.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
multidict==6.6.4
mypy_extensions==1.1.0
narwhals==2.1.0
numpy==2.3.2
openai==1.99.8
orjson==3.11.1
ormsgpack==1.10.0
packaging==25.0
pandas==2.3.1
pillow==11.3.0
primp==0.15.0
propcache==0.3.2
protobuf==6.31.1
pyarrow==21.0.0
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
pydeck==0.9.1
pypdf==6.0.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
pytz==2025.2
PyYAML==6.0.2
referencing==0.36.2
regex==2025.7.34
requests==2.32.4
requests-toolbelt==1.0.0
rpds-py==0.27.0
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
SQLAlchemy==2.0.43
sqlite-vec==0.1.6
streamlit==1.48.0
tenacity==9.1.2
tiktoken==0.11.0
toml==0.10.2
tornado==6.5.2
tqdm==4.67.1
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2025.2
urllib3==2.5.0
xxhash==3.5.0
yarl==1.20.1
youtube-transcript-api==0.6.2
zstandard==0.23.0
